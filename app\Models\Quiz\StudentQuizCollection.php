<?php

namespace App\Models\Quiz;

use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Models\Student\Student;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class StudentQuizCollection extends Model
{
    use HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'student_quiz_collections';

    protected $casts = [
        'is_default' => 'boolean',
        'meta' => 'array',
    ];

    public function getModelName(): string
    {
        return 'StudentQuizCollection';
    }

    public function student(): BelongsTo
    {
        return $this->belongsTo(Student::class);
    }

    public function items(): HasMany
    {
        return $this->hasMany(QuizCollectionItem::class, 'collection_id');
    }

    public function scopeByStudent(Builder $query, ?int $studentId = null)
    {
        if ($studentId) {
            $query->whereStudentId($studentId);
        }
    }

    public function scopeDefault(Builder $query)
    {
        $query->where('is_default', true);
    }

    public function scopeFindByUuidOrFail(Builder $query, string $uuid, $field = 'message')
    {
        return $query
            ->where('uuid', $uuid)
            ->getOrFail(trans('quiz.collection'), $field);
    }

    public function getTotalQuizzesAttribute(): int
    {
        return $this->items()->count();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('student_quiz_collection')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
