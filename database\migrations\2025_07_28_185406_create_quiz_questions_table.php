<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_questions', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->index()->unique();

            $table->foreignId('quiz_id')->constrained('quizzes')->onDelete('cascade');

            $table->text('question_text')->nullable();
            $table->enum('question_type', ['mcq', 'single_line', 'multi_line', 'true_false'])->default('mcq');
            $table->decimal('points', 8, 2)->default(1.00);
            $table->integer('position')->default(0);

            // MCQ specific
            $table->json('options')->nullable()->comment('Array of options for MCQ');
            $table->json('correct_answers')->nullable()->comment('Array of correct answer indices/values');

            // Configuration
            $table->boolean('is_required')->default(true);
            $table->text('explanation')->nullable();

            $table->json('config')->nullable();
            $table->json('meta')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_questions');
    }
};
