<?php

namespace App\Models\Quiz;

use App\Casts\DateTimeCast;
use App\Concerns\HasConfig;
use App\Concerns\HasFilter;
use App\Concerns\HasMedia;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Models\Academic\Period;
use App\Models\Team;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Quiz extends Model
{
    use HasConfig, HasFactory, HasFilter, HasMedia, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'quizzes';

    protected $casts = [
        'is_public' => 'boolean',
        'allow_peer_sharing' => 'boolean',
        'allow_result_sharing' => 'boolean',
        'discovery_enabled' => 'boolean',
        'published_at' => DateTimeCast::class,
        'config' => 'array',
        'meta' => 'array',
    ];

    public function getModelName(): string
    {
        return 'Quiz';
    }

    public function period(): BelongsTo
    {
        return $this->belongsTo(Period::class);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(QuizCategory::class, 'category_id');
    }

    public function questions(): HasMany
    {
        return $this->hasMany(QuizQuestion::class);
    }

    public function attempts(): HasMany
    {
        return $this->hasMany(QuizAttempt::class);
    }

    public function participants(): HasMany
    {
        return $this->hasMany(QuizParticipant::class);
    }

    public function shares(): HasMany
    {
        return $this->hasMany(QuizShare::class);
    }

    public function scopeByTeam(Builder $query, ?int $teamId = null)
    {
        $teamId = $teamId ?? auth()->user()?->current_team_id;

        $query->whereTeamId($teamId);
    }

    public function scopeByPeriod(Builder $query, ?int $periodId = null)
    {
        if ($periodId) {
            $query->wherePeriodId($periodId);
        }
    }

    public function scopePublished(Builder $query)
    {
        $query->where('status', 'published');
    }

    public function scopePublic(Builder $query)
    {
        $query->where('is_public', true);
    }

    public function scopeDiscoverable(Builder $query)
    {
        $query->where('discovery_enabled', true);
    }

    public function scopeFindByUuidOrFail(Builder $query, string $uuid, $field = 'message')
    {
        return $query
            ->byTeam()
            ->where('uuid', $uuid)
            ->getOrFail(trans('quiz.quiz'), $field);
    }

    public function scopeFindByUniqueCodeOrFail(Builder $query, string $code, $field = 'message')
    {
        return $query
            ->where('unique_code', $code)
            ->where('is_public', true)
            ->getOrFail(trans('quiz.quiz'), $field);
    }

    public function getIsPublishedAttribute(): bool
    {
        return $this->status === 'published';
    }

    public function getIsActiveAttribute(): bool
    {
        return $this->status === 'published' && !$this->isExpired();
    }

    public function isExpired(): bool
    {
        // Add expiry logic if needed
        return false;
    }

    public function getTotalQuestionsAttribute(): int
    {
        return $this->questions()->count();
    }

    public function getTotalAttemptsAttribute(): int
    {
        return $this->attempts()->count();
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('quiz')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
