<?php

namespace App\Listeners;

use App\Jobs\EvaluatePaymentRestrictionsJob;
use App\Models\Finance\Transaction;
use App\Models\Student\Student;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class EvaluatePaymentRestrictionsOnPayment
{
    /**
     * Handle the event.
     */
    public function handle($event): void
    {
        // Check if payment restrictions are enabled
        if (!config('config.finance.enable_payment_restrictions', true)) {
            return;
        }

        try {
            // Get the transaction from the event
            $transaction = $this->getTransactionFromEvent($event);
            
            if (!$transaction) {
                return;
            }

            // Get affected users
            $users = $this->getAffectedUsers($transaction);
            
            if ($users->isEmpty()) {
                return;
            }

            Log::info('Triggering payment restriction evaluation after payment', [
                'transaction_id' => $transaction->id,
                'transaction_uuid' => $transaction->uuid,
                'affected_users' => $users->pluck('id')->toArray(),
            ]);

            // Dispatch evaluation job for each affected user
            foreach ($users as $user) {
                EvaluatePaymentRestrictionsJob::dispatch(
                    $transaction->period_id,
                    $user->id
                )->delay(now()->addMinutes(1)); // Small delay to ensure transaction is fully processed
            }

        } catch (\Exception $e) {
            Log::error('Error triggering payment restriction evaluation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Extract transaction from various event types
     */
    private function getTransactionFromEvent($event): ?Transaction
    {
        // Handle different event types that might contain transaction data
        if (isset($event->transaction) && $event->transaction instanceof Transaction) {
            return $event->transaction;
        }

        if (isset($event->model) && $event->model instanceof Transaction) {
            return $event->model;
        }

        // If event has transaction ID
        if (isset($event->transactionId)) {
            return Transaction::find($event->transactionId);
        }

        return null;
    }

    /**
     * Get users affected by this transaction
     */
    private function getAffectedUsers(Transaction $transaction): \Illuminate\Support\Collection
    {
        $users = collect();

        try {
            // If transaction is related to a student
            if ($transaction->transactionable_type === 'Student' && $transaction->transactionable_id) {
                $student = Student::find($transaction->transactionable_id);
                
                if ($student) {
                    // Get student's user account
                    $studentUsers = User::query()
                        ->whereHas('roles', function ($q) {
                            $q->where('name', 'student');
                        })
                        ->whereHas('contact.accounts', function ($q) use ($student) {
                            $q->where('accountable_type', 'Contact')
                              ->where('accountable_id', $student->contact_id);
                        })
                        ->get();

                    $users = $users->merge($studentUsers);

                    // Get guardian users for this student
                    $guardianUsers = User::query()
                        ->whereHas('roles', function ($q) {
                            $q->where('name', 'guardian');
                        })
                        ->whereHas('contact.accounts', function ($q) use ($student) {
                            $q->whereHas('accountable.guardians', function ($gq) use ($student) {
                                $gq->where('student_id', $student->id);
                            });
                        })
                        ->get();

                    $users = $users->merge($guardianUsers);
                }
            }

        } catch (\Exception $e) {
            Log::error('Error getting affected users for payment restriction evaluation', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $users->unique('id');
    }
}
