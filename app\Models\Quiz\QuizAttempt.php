<?php

namespace App\Models\Quiz;

use App\Casts\DateTimeCast;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class QuizAttempt extends Model
{
    use HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'quiz_attempts';

    protected $casts = [
        'started_at' => DateTimeCast::class,
        'completed_at' => DateTimeCast::class,
        'submitted_at' => DateTimeCast::class,
        'answers' => 'array',
        'score' => 'decimal:2',
        'percentage' => 'decimal:2',
        'meta' => 'array',
    ];

    public function getModelName(): string
    {
        return 'QuizAttempt';
    }

    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeByUser(Builder $query, ?int $userId = null)
    {
        $userId = $userId ?? auth()->id();

        $query->whereUserId($userId);
    }

    public function scopeCompleted(Builder $query)
    {
        $query->where('status', 'completed');
    }

    public function scopeInProgress(Builder $query)
    {
        $query->where('status', 'in_progress');
    }

    public function getIsCompletedAttribute(): bool
    {
        return $this->status === 'completed';
    }

    public function getIsInProgressAttribute(): bool
    {
        return $this->status === 'in_progress';
    }

    public function getIsAbandoned(): bool
    {
        return $this->status === 'abandoned';
    }

    public function getDurationInMinutesAttribute(): ?int
    {
        if (!$this->started_at->value || !$this->completed_at->value) {
            return null;
        }

        return $this->started_at->value->diffInMinutes($this->completed_at->value);
    }

    public function getFormattedScoreAttribute(): string
    {
        return number_format($this->score, 2);
    }

    public function getFormattedPercentageAttribute(): string
    {
        return number_format($this->percentage, 1) . '%';
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('quiz_attempt')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
