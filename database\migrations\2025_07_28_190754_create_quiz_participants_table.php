<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quiz_participants', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->index()->unique();

            $table->foreignId('quiz_id')->constrained('quizzes')->onDelete('cascade');
            $table->string('name', 100)->nullable();
            $table->string('email', 100)->nullable();
            $table->string('phone', 20)->nullable();
            $table->text('additional_info')->nullable();

            // Registration status
            $table->enum('status', ['pending', 'approved', 'rejected', 'blocked'])->default('pending');
            $table->timestamp('registered_at')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->foreignId('approved_by_user_id')->nullable()->constrained('users')->onDelete('set null');

            // Tracking
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();

            $table->json('meta')->nullable();
            $table->timestamps();

            // Unique constraint for email per quiz
            $table->unique(['quiz_id', 'email']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quiz_participants');
    }
};
