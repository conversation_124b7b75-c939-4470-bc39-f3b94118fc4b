<?php

namespace App\Observers\Finance;

use App\Jobs\EvaluatePaymentRestrictionsJob;
use App\Models\Finance\Transaction;
use App\Models\Student\Student;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class TransactionObserver
{
    /**
     * Handle the Transaction "created" event.
     */
    public function created(Transaction $transaction): void
    {
        $this->evaluatePaymentRestrictions($transaction);
    }

    /**
     * Handle the Transaction "updated" event.
     */
    public function updated(Transaction $transaction): void
    {
        // Only trigger evaluation if the transaction status changed to processed/succeed
        if ($transaction->wasChanged(['processed_at', 'status']) && $transaction->isProcessed()) {
            $this->evaluatePaymentRestrictions($transaction);
        }
    }

    /**
     * Evaluate payment restrictions for affected users
     */
    private function evaluatePaymentRestrictions(Transaction $transaction): void
    {
        // Check if payment restrictions are enabled
        if (!config('config.finance.enable_payment_restrictions', true)) {
            return;
        }

        // Only process receipt transactions (payments received)
        if ($transaction->type->value !== 'receipt') {
            return;
        }

        // Clear payment restriction session cache for affected users to force immediate re-evaluation
        $this->clearPaymentRestrictionCache($transaction);

        try {
            // Get affected users
            $users = $this->getAffectedUsers($transaction);
            
            if ($users->isEmpty()) {
                return;
            }

            Log::info('Triggering payment restriction evaluation after transaction', [
                'transaction_id' => $transaction->id,
                'transaction_uuid' => $transaction->uuid,
                'transaction_type' => $transaction->type->value,
                'affected_users' => $users->pluck('id')->toArray(),
            ]);

            // Dispatch evaluation job for each affected user with a small delay
            foreach ($users as $user) {
                EvaluatePaymentRestrictionsJob::dispatch(
                    $transaction->period_id,
                    $user->id
                )->delay(now()->addMinutes(1));
            }

        } catch (\Exception $e) {
            Log::error('Error triggering payment restriction evaluation from transaction observer', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Clear payment restriction session cache for users affected by this payment
     */
    private function clearPaymentRestrictionCache(Transaction $transaction): void
    {
        try {
            // Get affected users and clear their session cache
            $users = $this->getAffectedUsers($transaction);

            foreach ($users as $user) {
                $this->clearUserSessionCache($user->id);
            }

        } catch (\Exception $e) {
            // Failed to clear payment restriction cache - users may need to wait for cache expiry
        }
    }

    /**
     * Clear payment restriction session cache for a specific user
     */
    private function clearUserSessionCache(int $userId): void
    {
        try {
            // If using database sessions, we can clear specific user sessions
            if (config('session.driver') === 'database') {
                // Find sessions for this user and clear the payment restriction cache
                \DB::table('sessions')
                    ->where('user_id', $userId)
                    ->update([
                        'payload' => \DB::raw("REPLACE(payload, '\"payment_restriction_last_check\"', '\"payment_restriction_last_check_cleared\"')")
                    ]);
            }

            // Session cache cleared for immediate re-evaluation

        } catch (\Exception $e) {
            // Failed to clear session cache - user may need to wait for cache expiry
        }
    }

    /**
     * Get users affected by this transaction
     */
    private function getAffectedUsers(Transaction $transaction): \Illuminate\Support\Collection
    {
        $users = collect();

        try {
            // If transaction is related to a student
            if ($transaction->transactionable_type === 'Student' && $transaction->transactionable_id) {
                $student = Student::find($transaction->transactionable_id);
                
                if ($student) {
                    // Get student's user account
                    $studentUsers = User::query()
                        ->whereHas('roles', function ($q) {
                            $q->where('name', 'student');
                        })
                        ->whereHas('contact.accounts', function ($q) use ($student) {
                            $q->where('accountable_type', 'Contact')
                              ->where('accountable_id', $student->contact_id);
                        })
                        ->get();

                    $users = $users->merge($studentUsers);

                    // Get guardian users for this student
                    $guardianUsers = User::query()
                        ->whereHas('roles', function ($q) {
                            $q->where('name', 'guardian');
                        })
                        ->whereHas('contact.accounts', function ($q) use ($student) {
                            $q->whereHas('accountable.guardians', function ($gq) use ($student) {
                                $gq->where('student_id', $student->id);
                            });
                        })
                        ->get();

                    $users = $users->merge($guardianUsers);
                }
            }

            // Also check if transaction has fee payments that can help identify students
            if ($users->isEmpty()) {
                $feePayments = $transaction->feePayments()->with('fee.student')->get();
                
                foreach ($feePayments as $feePayment) {
                    if ($feePayment->fee && $feePayment->fee->student) {
                        $student = $feePayment->fee->student;
                        
                        // Get student's user account
                        $studentUsers = User::query()
                            ->whereHas('roles', function ($q) {
                                $q->where('name', 'student');
                            })
                            ->whereHas('contact.accounts', function ($q) use ($student) {
                                $q->where('accountable_type', 'Contact')
                                  ->where('accountable_id', $student->contact_id);
                            })
                            ->get();

                        $users = $users->merge($studentUsers);

                        // Get guardian users for this student
                        $guardianUsers = User::query()
                            ->whereHas('roles', function ($q) {
                                $q->where('name', 'guardian');
                            })
                            ->whereHas('contact.accounts', function ($q) use ($student) {
                                $q->whereHas('accountable.guardians', function ($gq) use ($student) {
                                    $gq->where('student_id', $student->id);
                                });
                            })
                            ->get();

                        $users = $users->merge($guardianUsers);
                    }
                }
            }

        } catch (\Exception $e) {
            Log::error('Error getting affected users for payment restriction evaluation', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $users->unique('id');
    }
}
