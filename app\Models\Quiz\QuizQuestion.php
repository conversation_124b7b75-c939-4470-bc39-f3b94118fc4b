<?php

namespace App\Models\Quiz;

use App\Concerns\HasConfig;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class QuizQuestion extends Model
{
    use HasConfig, HasFactory, HasFilter, HasMeta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'quiz_questions';

    protected $casts = [
        'options' => 'array',
        'correct_answers' => 'array',
        'is_required' => 'boolean',
        'points' => 'decimal:2',
        'config' => 'array',
        'meta' => 'array',
    ];

    public function getModelName(): string
    {
        return 'QuizQuestion';
    }

    public function quiz(): BelongsTo
    {
        return $this->belongsTo(Quiz::class);
    }

    /**
     * Get the question text attribute - return HTML content directly like BaseEditor
     */
    public function getQuestionTextAttribute($value): ?string
    {
        // Return HTML content directly (like BaseEditor pattern)
        return $value;
    }

    /**
     * Get the explanation attribute - return HTML content directly like BaseEditor
     */
    public function getExplanationAttribute($value): ?string
    {
        // Return HTML content directly (like BaseEditor pattern)
        return $value;
    }

    public function getIsMcqAttribute(): bool
    {
        return $this->question_type === 'mcq';
    }

    public function getIsTrueFalseAttribute(): bool
    {
        return $this->question_type === 'true_false';
    }

    public function getIsSingleLineAttribute(): bool
    {
        return $this->question_type === 'single_line';
    }

    public function getIsMultiLineAttribute(): bool
    {
        return $this->question_type === 'multi_line';
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName('quiz_question')
            ->logAll()
            ->logExcept(['updated_at'])
            ->logOnlyDirty();
    }
}
