<?php

namespace App\Http\Middleware;

use App\Actions\Finance\CheckPaymentRestrictions as CheckPaymentRestrictionsAction;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckPaymentRestrictions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Skip if user is not authenticated
        if (!Auth::check()) {
            return $next($request);
        }

        $user = Auth::user();

        // Skip for admin users and non-student/guardian users
        if ($user->is_default || $user->hasRole('admin') || !$user->hasRole(['student', 'guardian'])) {
            return $next($request);
        }

        // Skip if payment restrictions are disabled
        if (!config('config.finance.enable_payment_restrictions', true)) {
            return $next($request);
        }

        // Check payment restrictions for students and guardians

        // // Skip for certain routes (like logout, guest payment, etc.)
        // $exemptRoutes = [
        //     'auth.logout',
        //     'guest-payment.*',
        //     'profile.*',
        //     'password.*',
        // ];

        // // Also skip for guest payment URLs and payment restriction page
        // $exemptPaths = [
        //     'app/payment',
        //     'app/payment/*',
        //     'app/guest-payments',
        //     'app/guest-payments/*',
        //     'app/payment-restriction',
        //     'app/payment-restriction/*',
        //     'api/*/app/guest-payments',
        //     'api/*/app/guest-payments/*',
        // ];

        // foreach ($exemptRoutes as $pattern) {
        //     if ($request->routeIs($pattern)) {
        //         return $next($request);
        //     }
        // }

        // // Check exempt paths
        // foreach ($exemptPaths as $path) {
        //     if ($request->is($path)) {
        //         return $next($request);
        //     }
        // }

        // Real-time middleware should have minimal caching to ensure immediate response to payments
        // Only skip if user was checked very recently (2 minutes max) to prevent API spam
        $lastCheck = session('payment_restriction_last_check');
        $shortCacheMinutes = 2; // Very short cache for real-time checks

        if ($lastCheck) {
            $lastCheckTime = \Carbon\Carbon::parse($lastCheck);
            $minutesSinceCheck = $lastCheckTime->diffInMinutes(now());

            if ($minutesSinceCheck < $shortCacheMinutes) {
                return $next($request);
            }
        }

        try {
            // Check payment restrictions
            (new CheckPaymentRestrictionsAction)->execute($request, $user);

            // Update last check time only when check passes
            session(['payment_restriction_last_check' => now()]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            // User is restricted
            // If it's a payment restriction error, handle it appropriately
            if ($e->errorBag === 'payment_restriction') {
                $errorData = $e->errorData ?? [];
                
                // For API requests, return JSON response
                if ($request->expectsJson()) {
                    return response()->json([
                        'message' => $e->getMessage(),
                        'errors' => $e->errors(),
                        'type' => 'payment_restriction',
                        'data' => $errorData,
                    ], 403);
                }
                
                // For web requests, logout and redirect to payment restriction page
                return redirect('/app/payment-restriction?' . http_build_query([
                    'message' => $e->getMessage(),
                    'guest_payment_enabled' => isset($errorData['guest_payment_enabled']) ? 'true' : 'false',
                    'guest_payment_url' => $errorData['guest_payment_url'] ?? '',
                ]));
            }
            
            // Re-throw other validation exceptions
            throw $e;
        }

        return $next($request);
    }
}
