<?php

namespace App\Models\Finance;

use App\Casts\DateCast;
use App\Casts\EnumCast;
use App\Casts\PriceCast;
use App\Concerns\HasFilter;
use App\Concerns\HasMeta;
use App\Concerns\HasUuid;
use App\Enums\Finance\PaymentRestrictionScope;
use App\Enums\Finance\PaymentRestrictionTarget;
use App\Enums\Finance\PaymentRestrictionType;
use App\Models\Academic\Period;
use App\Models\Student\Student;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class PaymentRestriction extends Model
{
    use HasFactory, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eta, HasUuid, LogsActivity;

    protected $guarded = [];

    protected $primaryKey = 'id';

    protected $table = 'payment_restrictions';

    protected $casts = [
        'type' => EnumCast::class.':'.PaymentRestrictionType::class,
        'scope' => EnumCast::class.':'.PaymentRestrictionScope::class,
        'target_users' => EnumCast::class.':'.PaymentRestrictionTarget::class,
        'percentage_threshold' => 'decimal:2',
        'fixed_amount' => PriceCast::class,
        'applicable_batches' => 'array',
        'applicable_students' => 'array',
        'is_active' => 'boolean',
        'redirect_to_guest_payment' => 'boolean',
        'grace_period_days' => 'integer',
        'effective_from' => DateCast::class,
        'effective_until' => DateCast::class,
        'config' => 'array',
        'meta' => 'array',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'type', 'scope', 'target_users', 'is_active'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    public function period(): BelongsTo
    {
        return $this->belongsTo(Period::class);
    }

    public function feeStructure(): BelongsTo
    {
        return $this->belongsTo(FeeStructure::class);
    }

    public function feeInstallment(): BelongsTo
    {
        return $this->belongsTo(FeeInstallment::class);
    }

    public function userRestrictions(): HasMany
    {
        return $this->hasMany(UserPaymentRestriction::class);
    }

    public function scopeByPeriod(Builder $query, $periodId = null)
    {
        $periodId = $periodId ?? auth()->user()->current_period_id;

        $query->wherePeriodId($periodId);
    }

    public function scopeActive(Builder $query)
    {
        $query->where('is_active', true);
    }

    public function scopeEffective(Builder $query, $date = null)
    {
        $date = $date ?? now()->toDateString();
        
        $query->where(function ($q) use ($date) {
            $q->whereNull('effective_from')
              ->orWhere('effective_from', '<=', $date);
        })->where(function ($q) use ($date) {
            $q->whereNull('effective_until')
              ->orWhere('effective_until', '>=', $date);
        });
    }

    public function getConfig(string $option, mixed $default = null)
    {
        return Arr::get($this->config, $option, $default);
    }

    public function isApplicableToStudent(Student $student): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->scope === PaymentRestrictionScope::GLOBAL) {
            return true;
        }

        if ($this->scope === PaymentRestrictionScope::BATCH) {
            $applicableBatches = $this->applicable_batches ?? [];
            return in_array($student->batch_id, $applicableBatches);
        }

        if ($this->scope === PaymentRestrictionScope::INDIVIDUAL) {
            $applicableStudents = $this->applicable_students ?? [];
            return in_array($student->id, $applicableStudents);
        }

        return false;
    }

    public function isApplicableToUser(User $user): bool
    {
        if (!$this->is_active) {
            return false;
        }

        // Check if user has student or guardian role
        $hasStudentRole = $user->hasRole('student');
        $hasGuardianRole = $user->hasRole('guardian');

        return match ($this->target_users) {
            PaymentRestrictionTarget::STUDENT => $hasStudentRole,
            PaymentRestrictionTarget::GUARDIAN => $hasGuardianRole,
            PaymentRestrictionTarget::BOTH => $hasStudentRole || $hasGuardianRole,
        };
    }

    public function isCurrentlyEffective(): bool
    {
        $now = now()->toDateString();
        
        $fromCheck = !$this->effective_from || $this->effective_from->value <= $now;
        $untilCheck = !$this->effective_until || $this->effective_until->value >= $now;
        
        return $fromCheck && $untilCheck;
    }
}
