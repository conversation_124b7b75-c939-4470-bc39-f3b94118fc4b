<?php

namespace App\Services\Finance;

use App\Enums\Finance\PaymentRestrictionType;
use App\Models\Finance\PaymentRestriction;
use App\Models\Finance\UserPaymentRestriction;
use App\Models\Guardian;
use App\Models\Student\Fee;
use App\Models\Student\Student;
use App\Models\User;
use Illuminate\Support\Collection;

class PaymentRestrictionEvaluationService
{
    public function evaluateUserRestrictions(User $user): Collection
    {
        // Step 1: Check if restriction exists at all

// Step 2: Check each scope individually
$byPeriod = PaymentRestriction::query()->byPeriod()->get();
dd('By period:', $byPeriod->count(), 'Period ID:', auth()->user()->current_period_id);

$active = PaymentRestriction::query()->byPeriod()->active()->get();
dd('Active:', $active->count());

$effective = PaymentRestriction::query()->byPeriod()->active()->effective()->get();
dd('Effective:', $effective->count(), 'Date:', now()->toDateString());
        $restrictions = PaymentRestriction::query()
            ->byPeriod()
            ->active()
            ->effective()
            ->where(function ($q) use ($user) {
                $q->where('target_users', 'both')
                  ->orWhere('target_users', $user->hasRole('student') ? 'student' : 'guardian');
            })
            ->get();

        $evaluationResults = collect();

         dd($restrictions, $evaluationResults);

        foreach ($restrictions as $restriction) {
            if ($restriction->isApplicableToUser($user)) {
                $result = $this->evaluateRestriction($user, $restriction);
                $evaluationResults->push($result);
            }
        }

        return $evaluationResults;
    }

    public function evaluateRestriction(User $user, PaymentRestriction $restriction): array
    {
        // Get the student(s) associated with this user
        $students = $this->getUserStudents($user);

        // If no students found, this could indicate a data issue
        if ($students->isEmpty()) {
            \Log::warning('PaymentRestrictionEvaluation: No students found for user - possible data issue', [
                'user_id' => $user->id,
                'user_roles' => $user->roles->pluck('name')->toArray(),
                'restriction_id' => $restriction->id,
            ]);

            // Return a result indicating no evaluation was possible
            return [
                'user_id' => $user->id,
                'restriction_id' => $restriction->id,
                'total_fees' => 0,
                'paid_amount' => 0,
                'outstanding_amount' => 0,
                'required_amount' => 0,
                'is_restricted' => false, // Cannot restrict if no students found
                'restriction_reason' => 'No student records found for evaluation',
                'fee_breakdown' => [],
                'payment_guidance' => ['message' => 'No student records found for this user'],
                'evaluation_status' => 'no_students_found',
            ];
        }

        $totalFees = 0;
        $totalPaid = 0;
        $isRestricted = false;
        $restrictionReason = '';
        $feeBreakdown = [];

        foreach ($students as $student) {
            if (!$restriction->isApplicableToStudent($student)) {
                continue;
            }

            $studentEvaluation = $this->evaluateStudentRestriction($student, $restriction);
            
            $totalFees += $studentEvaluation['total_fees'];
            $totalPaid += $studentEvaluation['paid_amount'];
            
            if ($studentEvaluation['is_restricted']) {
                $isRestricted = true;
                $restrictionReason = $studentEvaluation['restriction_reason'];
            }
            
            $feeBreakdown[] = [
                'student_id' => $student->id,
                'student_name' => $student->name ?? $student->getAttribute('name'),
                'total_fees' => $studentEvaluation['total_fees'],
                'paid_amount' => $studentEvaluation['paid_amount'],
                'outstanding_amount' => $studentEvaluation['outstanding_amount'],
                'is_restricted' => $studentEvaluation['is_restricted'],
            ];
        }

        $requiredAmount = $this->calculateRequiredAmount($restriction, $totalFees);
        $outstandingAmount = max(0, $requiredAmount - $totalPaid);

        return [
            'user_id' => $user->id,
            'restriction_id' => $restriction->id,
            'total_fees' => $totalFees,
            'paid_amount' => $totalPaid,
            'outstanding_amount' => $outstandingAmount,
            'required_amount' => $requiredAmount,
            'is_restricted' => $isRestricted,
            'restriction_reason' => $restrictionReason,
            'fee_breakdown' => $feeBreakdown,
            'payment_guidance' => $this->generatePaymentGuidance($user, $restriction, $totalFees, $totalPaid, $requiredAmount, $feeBreakdown),
        ];
    }

    private function evaluateStudentRestriction(Student $student, PaymentRestriction $restriction): array
    {
        $fees = $this->getApplicableFees($student, $restriction);

        // Evaluate student restriction

        $totalFees = $fees->sum('total.value');
        $totalPaid = $fees->sum('paid.value');
        $outstandingAmount = $totalFees - $totalPaid;

        $requiredAmount = $this->calculateRequiredAmount($restriction, $totalFees);
        $paymentRequirementMet = $totalPaid >= $requiredAmount;

        // Check if grace period has expired for unpaid fees
        $gracePeriodExpired = $this->isGracePeriodExpired($fees, $restriction);

        // Only restrict if payment requirement is not met AND grace period has expired
        $isRestricted = !$paymentRequirementMet && $gracePeriodExpired;

        // Calculation completed

        $restrictionReason = '';
        if ($isRestricted) {
            $restrictionReason = $this->generateRestrictionReason($restriction, $totalPaid, $requiredAmount, $totalFees, $gracePeriodExpired);
        }

        return [
            'total_fees' => $totalFees,
            'paid_amount' => $totalPaid,
            'outstanding_amount' => $outstandingAmount,
            'required_amount' => $requiredAmount,
            'is_restricted' => $isRestricted,
            'restriction_reason' => $restrictionReason,
            'grace_period_expired' => $gracePeriodExpired,
        ];
    }

    private function getApplicableFees(Student $student, PaymentRestriction $restriction): Collection
    {
        // Get applicable fees for student

        $query = Fee::query()
            ->where('student_id', $student->id)
            ->whereHas('installment', function ($q) {
                $q->whereHas('structure', function ($sq) {
                    $sq->byPeriod();
                });
            });

        if ($restriction->type === PaymentRestrictionType::SPECIFIC_FEE) {
            if ($restriction->fee_structure_id) {
                $query->whereHas('installment', function ($q) use ($restriction) {
                    $q->where('fee_structure_id', $restriction->fee_structure_id);
                });
            }

            if ($restriction->fee_installment_id) {
                $query->where('fee_installment_id', $restriction->fee_installment_id);
            }
        }

        $fees = $query->get();

        // Fees retrieved

        return $fees;
    }

    private function isGracePeriodExpired(Collection $fees, PaymentRestriction $restriction): bool
    {
        // If grace period is 0, restriction applies immediately
        if ($restriction->grace_period_days <= 0) {
            return true;
        }

        // Find the earliest due date among unpaid fees
        $earliestDueDate = null;

        foreach ($fees as $fee) {
            $balance = $fee->getBalance()->value;

            // Skip fully paid fees
            if ($balance <= 0) {
                continue;
            }

            $dueDate = $fee->getDueDate();
            if ($dueDate && $dueDate->value) {
                if ($earliestDueDate === null || $dueDate->value < $earliestDueDate) {
                    $earliestDueDate = $dueDate->value;
                }
            }
        }

        // If no due date found, don't apply restriction
        if ($earliestDueDate === null) {
            return false;
        }

        // Calculate grace period end date
        $gracePeriodEndDate = \Carbon\Carbon::parse($earliestDueDate)->addDays($restriction->grace_period_days);

        // Grace period has expired if current date is after grace period end date
        return now()->isAfter($gracePeriodEndDate);
    }

    private function calculateRequiredAmount(PaymentRestriction $restriction, float $totalFees): float
    {
        return match ($restriction->type) {
            PaymentRestrictionType::PERCENTAGE => ($totalFees * $restriction->percentage_threshold) / 100,
            PaymentRestrictionType::FIXED_AMOUNT => $restriction->fixed_amount->value,
            PaymentRestrictionType::SPECIFIC_FEE => $totalFees, // Must pay the full amount for specific fees
        };
    }

    private function generateRestrictionReason(PaymentRestriction $restriction, float $paidAmount, float $requiredAmount, float $totalFees, bool $gracePeriodExpired = true): string
    {
        $shortfall = $requiredAmount - $paidAmount;
        
        $gracePeriodText = $restriction->grace_period_days > 0
            ? sprintf(' The %d-day grace period has expired.', $restriction->grace_period_days)
            : '';

        return match ($restriction->type) {
            PaymentRestrictionType::PERCENTAGE => sprintf(
                'You must pay at least %s%% (%s) of your total fees (%s). You have paid %s, leaving a shortfall of %s.%s',
                $restriction->percentage_threshold,
                \Price::from($requiredAmount)->formatted,
                \Price::from($totalFees)->formatted,
                \Price::from($paidAmount)->formatted,
                \Price::from($shortfall)->formatted,
                $gracePeriodText
            ),
            PaymentRestrictionType::FIXED_AMOUNT => sprintf(
                'You must pay at least %s. You have paid %s, leaving a shortfall of %s.%s',
                $restriction->fixed_amount->formatted,
                \Price::from($paidAmount)->formatted,
                \Price::from($shortfall)->formatted,
                $gracePeriodText
            ),
            PaymentRestrictionType::SPECIFIC_FEE => sprintf(
                'You must pay the full amount for %s (%s). You have paid %s, leaving a shortfall of %s.%s',
                $restriction->feeStructure?->name ?? $restriction->feeInstallment?->title ?? 'specific fee',
                \Price::from($totalFees)->formatted,
                \Price::from($paidAmount)->formatted,
                \Price::from($shortfall)->formatted,
                $gracePeriodText
            ),
        };
    }

    private function getUserStudents(User $user): Collection
    {
        // Get students for the user based on their role

        if ($user->hasRole('student')) {
            // For student users, get their own student record using correct relationship pattern
            $students = Student::query()
                ->byPeriod()
                ->join('contacts', function ($join) use ($user) {
                    $join->on('students.contact_id', '=', 'contacts.id')
                        ->where('contacts.team_id', $user->current_team_id)
                        ->where('contacts.user_id', $user->id);
                })
                ->select('students.*', \DB::raw('REGEXP_REPLACE(CONCAT_WS(" ", contacts.first_name, contacts.middle_name, contacts.third_name, contacts.last_name), "[[:space:]]+", " ") as name'))
                ->get();

            // Student records found

            return $students;
        }

        if ($user->hasRole('guardian')) {
            // For guardian users, get all students they are guardian for using correct relationship pattern
            $guardianContactIds = Guardian::query()
                ->select('guardians.primary_contact_id')
                ->join('contacts', 'guardians.contact_id', '=', 'contacts.id')
                ->where('contacts.team_id', $user->current_team_id)
                ->where('contacts.user_id', $user->id)
                ->pluck('guardians.primary_contact_id')
                ->all();

            // Guardian contact IDs found

            $students = Student::query()
                ->byPeriod()
                ->join('contacts', 'students.contact_id', '=', 'contacts.id')
                ->whereIn('students.contact_id', $guardianContactIds)
                ->where('students.period_id', $user->current_period_id)
                ->select('students.*', \DB::raw('REGEXP_REPLACE(CONCAT_WS(" ", contacts.first_name, contacts.middle_name, contacts.third_name, contacts.last_name), "[[:space:]]+", " ") as name'))
                ->get();

            // Ward students found

            return $students;
        }

        // User has no applicable role

        return collect();
    }

    public function updateUserRestrictionStatus(User $user, array $evaluationResult): UserPaymentRestriction
    {
        // Get the restrictable entity (student) for this user
        $students = $this->getUserStudents($user);
        $restrictableId = $students->isNotEmpty() ? $students->first()->id : null;
        $restrictableType = $students->isNotEmpty() ? 'App\\Models\\Student\\Student' : null;

        // Use updateOrCreate to handle duplicate key constraint
        $userRestriction = UserPaymentRestriction::updateOrCreate(
            [
                'user_id' => $user->id,
                'payment_restriction_id' => $evaluationResult['restriction_id'],
            ],
            [
                'restrictable_id' => $restrictableId,
                'restrictable_type' => $restrictableType,
                'total_fees' => $evaluationResult['total_fees'],
                'paid_amount' => $evaluationResult['paid_amount'],
                'outstanding_amount' => $evaluationResult['outstanding_amount'],
                'required_amount' => $evaluationResult['required_amount'],
                'fee_breakdown' => $evaluationResult['fee_breakdown'],
                'last_checked_at' => now(),
            ]
        );

        // Update restriction status
        if ($evaluationResult['is_restricted']) {
            $userRestriction->update([
                'is_restricted' => true,
                'restricted_at' => $userRestriction->restricted_at ?? now(),
                'restriction_reason' => $evaluationResult['restriction_reason'],
                'resolved_at' => null,
            ]);
        } else {
            $userRestriction->update([
                'is_restricted' => false,
                'resolved_at' => now(),
                'restriction_reason' => null,
            ]);
        }

        return $userRestriction;
    }

    /**
     * Get grace period information for a student and restriction
     */
    public function getGracePeriodInfo(Student $student, PaymentRestriction $restriction): array
    {
        $fees = $this->getApplicableFees($student, $restriction);

        // Find the earliest due date among unpaid fees
        $earliestDueDate = null;
        $unpaidFeesCount = 0;

        foreach ($fees as $fee) {
            if ($fee->getBalance()->value > 0) {
                $unpaidFeesCount++;
                $dueDate = $fee->getDueDate();
                if ($dueDate && $dueDate->value) {
                    if ($earliestDueDate === null || $dueDate->value < $earliestDueDate) {
                        $earliestDueDate = $dueDate->value;
                    }
                }
            }
        }

        if ($earliestDueDate === null) {
            return [
                'has_unpaid_fees' => $unpaidFeesCount > 0,
                'unpaid_fees_count' => $unpaidFeesCount,
                'earliest_due_date' => null,
                'grace_period_days' => $restriction->grace_period_days,
                'grace_period_end_date' => null,
                'grace_period_expired' => false,
                'days_until_restriction' => null,
            ];
        }

        $gracePeriodEndDate = \Carbon\Carbon::parse($earliestDueDate)->addDays($restriction->grace_period_days);
        $gracePeriodExpired = now()->isAfter($gracePeriodEndDate);
        $daysUntilRestriction = $gracePeriodExpired ? 0 : now()->diffInDays($gracePeriodEndDate);

        return [
            'has_unpaid_fees' => $unpaidFeesCount > 0,
            'unpaid_fees_count' => $unpaidFeesCount,
            'earliest_due_date' => $earliestDueDate,
            'grace_period_days' => $restriction->grace_period_days,
            'grace_period_end_date' => $gracePeriodEndDate->toDateString(),
            'grace_period_expired' => $gracePeriodExpired,
            'days_until_restriction' => $daysUntilRestriction,
        ];
    }

    private function generatePaymentGuidance(User $user, PaymentRestriction $restriction, float $totalFees, float $totalPaid, float $requiredAmount, array $feeBreakdown): array
    {
        $amountNeeded = max(0, $requiredAmount - $totalPaid);
        $paymentPercentage = $totalFees > 0 ? round(($totalPaid / $totalFees) * 100, 2) : 100;

        $guidance = [
            'amount_needed' => $amountNeeded,
            'payment_percentage' => $paymentPercentage,
            'is_payment_required' => $amountNeeded > 0,
        ];

        if ($user->hasRole('guardian') && count($feeBreakdown) > 1) {
            // For guardians with multiple wards, show payment options
            $paymentOptions = [];

            foreach ($feeBreakdown as $wardFee) {
                if ($wardFee['outstanding_amount'] > 0) {
                    $canPayFull = $wardFee['outstanding_amount'] >= $amountNeeded;
                    $paymentOptions[] = [
                        'student_name' => $wardFee['student_name'],
                        'outstanding_amount' => $wardFee['outstanding_amount'],
                        'can_pay_full_requirement' => $canPayFull,
                        'suggested_amount' => $canPayFull ? $amountNeeded : $wardFee['outstanding_amount'],
                    ];
                }
            }

            $guidance['payment_options'] = $paymentOptions;
            $guidance['message'] = $this->generateGuardianPaymentMessage($restriction, $amountNeeded, $paymentOptions);
        } else {
            // For students or single-ward guardians
            $guidance['message'] = $this->generateStudentPaymentMessage($restriction, $amountNeeded, $totalFees);
        }

        return $guidance;
    }

    private function generateGuardianPaymentMessage(PaymentRestriction $restriction, float $amountNeeded, array $paymentOptions): string
    {
        if ($amountNeeded <= 0) {
            return 'All payment requirements have been met.';
        }

        $formattedAmount = \Price::from($amountNeeded)->formatted;
        $restrictionType = match ($restriction->type) {
            PaymentRestrictionType::PERCENTAGE => $restriction->percentage_threshold . '% payment threshold',
            PaymentRestrictionType::FIXED_AMOUNT => 'fixed amount requirement',
            PaymentRestrictionType::SPECIFIC_FEE => 'specific fee requirement',
        };

        $message = "You need to pay {$formattedAmount} more to meet the {$restrictionType}. ";

        $canPayOptions = array_filter($paymentOptions, fn($option) => $option['can_pay_full_requirement']);

        if (!empty($canPayOptions)) {
            $studentNames = array_column($canPayOptions, 'student_name');
            $message .= "You can pay this amount towards any of these students: " . implode(', ', $studentNames) . ".";
        } else {
            $message .= "You can make partial payments towards any student with outstanding fees.";
        }

        return $message;
    }

    private function generateStudentPaymentMessage(PaymentRestriction $restriction, float $amountNeeded, float $totalFees): string
    {
        if ($amountNeeded <= 0) {
            return 'All payment requirements have been met.';
        }

        $formattedAmount = \Price::from($amountNeeded)->formatted;
        $restrictionType = match ($restriction->type) {
            PaymentRestrictionType::PERCENTAGE => $restriction->percentage_threshold . '% of your total fees',
            PaymentRestrictionType::FIXED_AMOUNT => 'the required fixed amount',
            PaymentRestrictionType::SPECIFIC_FEE => 'the specific fee amount',
        };

        return "You need to pay {$formattedAmount} more to meet {$restrictionType}.";
    }
}
